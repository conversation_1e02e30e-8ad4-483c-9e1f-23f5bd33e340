#!/usr/bin/env python3
"""
Test script to verify borrowing limit enforcement is working correctly.
This script tests both backend validation and settings functionality.
"""

import sys
import os
import requests
import json
from datetime import datetime, timedelta

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, User, Book, Circulation, Settings

# Test configuration
BASE_URL = 'http://localhost:5000'
TEST_ADMIN_CREDENTIALS = {
    'user_id': 'admin',
    'password': 'admin123'
}

def get_auth_token():
    """Get authentication token for admin user"""
    response = requests.post(f'{BASE_URL}/api/auth/login', json=TEST_ADMIN_CREDENTIALS)
    if response.status_code == 200:
        return response.json()['access_token']
    else:
        print(f"Failed to authenticate: {response.text}")
        return None

def test_settings_api(token):
    """Test that settings API is working correctly"""
    print("\n=== Testing Settings API ===")
    
    headers = {'Authorization': f'Bearer {token}'}
    
    # Test GET settings
    response = requests.get(f'{BASE_URL}/api/admin/settings', headers=headers)
    if response.status_code == 200:
        settings = response.json()['settings']
        print(f"✓ GET settings successful: {settings}")
        
        # Verify default values
        assert settings['max_books_per_student'] == 3, "Default student limit should be 3"
        assert settings['max_books_per_staff'] == 5, "Default staff limit should be 5"
        print("✓ Default settings values are correct")
    else:
        print(f"✗ GET settings failed: {response.text}")
        return False
    
    # Test POST settings (update limits)
    new_settings = {
        'settings': {
            'max_books_per_student': 2,  # Reduce to 2 for testing
            'max_books_per_staff': 4,    # Reduce to 4 for testing
            'loan_period_days': 14,
            'daily_fine_rate': 1.0,
            'max_renewal_count': 2,
            'renewal_period_days': 7,
            'overdue_grace_period': 0
        }
    }
    
    response = requests.post(f'{BASE_URL}/api/admin/settings', headers=headers, json=new_settings)
    if response.status_code == 200:
        print("✓ POST settings successful")
        
        # Verify settings were updated
        response = requests.get(f'{BASE_URL}/api/admin/settings', headers=headers)
        updated_settings = response.json()['settings']
        assert updated_settings['max_books_per_student'] == 2, "Student limit should be updated to 2"
        assert updated_settings['max_books_per_staff'] == 4, "Staff limit should be updated to 4"
        print("✓ Settings were updated correctly")
    else:
        print(f"✗ POST settings failed: {response.text}")
        return False
    
    return True

def test_borrowing_limits(token):
    """Test borrowing limit enforcement"""
    print("\n=== Testing Borrowing Limit Enforcement ===")
    
    headers = {'Authorization': f'Bearer {token}'}
    
    # Find a test student
    with app.app_context():
        test_student = User.query.filter_by(role='student').first()
        if not test_student:
            print("✗ No test student found in database")
            return False
        
        # Clear any existing circulations for clean test
        Circulation.query.filter_by(user_id=test_student.id, status='issued').delete()
        db.session.commit()
        
        # Find available books for testing
        available_books = Book.query.filter(Book.available_copies > 0).limit(5).all()
        if len(available_books) < 3:
            print("✗ Not enough available books for testing")
            return False
        
        print(f"Testing with student: {test_student.user_id} ({test_student.name})")
        
        # Test issuing books up to the limit (2 books as per updated settings)
        issued_books = []
        for i in range(2):  # Current limit is 2
            book = available_books[i]
            due_date = (datetime.now() + timedelta(days=14)).strftime('%Y-%m-%d')
            
            issue_data = {
                'user_id': test_student.user_id,
                'book_id': book.id,
                'due_date': due_date
            }
            
            response = requests.post(f'{BASE_URL}/api/admin/circulation/issue', 
                                   headers=headers, json=issue_data)
            
            if response.status_code == 201:
                print(f"✓ Successfully issued book {i+1}: {book.title}")
                issued_books.append(book)
            else:
                print(f"✗ Failed to issue book {i+1}: {response.text}")
                return False
        
        # Test issuing one more book (should fail due to limit)
        book = available_books[2]
        due_date = (datetime.now() + timedelta(days=14)).strftime('%Y-%m-%d')
        
        issue_data = {
            'user_id': test_student.user_id,
            'book_id': book.id,
            'due_date': due_date
        }
        
        response = requests.post(f'{BASE_URL}/api/admin/circulation/issue', 
                               headers=headers, json=issue_data)
        
        if response.status_code == 400:
            error_message = response.json().get('error', '')
            if 'maximum borrowing limit' in error_message:
                print(f"✓ Borrowing limit correctly enforced: {error_message}")
            else:
                print(f"✗ Unexpected error message: {error_message}")
                return False
        else:
            print(f"✗ Expected borrowing limit error, but got: {response.status_code} - {response.text}")
            return False
        
        # Clean up - return the books
        for book in issued_books:
            circulation = Circulation.query.filter_by(
                user_id=test_student.id, 
                book_id=book.id, 
                status='issued'
            ).first()
            if circulation:
                circulation.status = 'returned'
                circulation.return_date = datetime.now()
                book.available_copies += 1
        
        db.session.commit()
        print("✓ Test cleanup completed")
    
    return True

def test_staff_limits(token):
    """Test staff borrowing limits"""
    print("\n=== Testing Staff Borrowing Limits ===")
    
    headers = {'Authorization': f'Bearer {token}'}
    
    with app.app_context():
        test_staff = User.query.filter_by(role='staff').first()
        if not test_staff:
            print("✗ No test staff found in database")
            return False
        
        # Clear any existing circulations
        Circulation.query.filter_by(user_id=test_staff.id, status='issued').delete()
        db.session.commit()
        
        # Find available books
        available_books = Book.query.filter(Book.available_copies > 0).limit(6).all()
        if len(available_books) < 5:
            print("✗ Not enough available books for staff testing")
            return False
        
        print(f"Testing with staff: {test_staff.user_id} ({test_staff.name})")
        
        # Issue books up to staff limit (4 as per updated settings)
        issued_books = []
        for i in range(4):
            book = available_books[i]
            due_date = (datetime.now() + timedelta(days=14)).strftime('%Y-%m-%d')
            
            issue_data = {
                'user_id': test_staff.user_id,
                'book_id': book.id,
                'due_date': due_date
            }
            
            response = requests.post(f'{BASE_URL}/api/admin/circulation/issue', 
                                   headers=headers, json=issue_data)
            
            if response.status_code == 201:
                print(f"✓ Successfully issued book {i+1} to staff")
                issued_books.append(book)
            else:
                print(f"✗ Failed to issue book {i+1} to staff: {response.text}")
                return False
        
        # Try to issue one more (should fail)
        book = available_books[4]
        due_date = (datetime.now() + timedelta(days=14)).strftime('%Y-%m-%d')
        
        issue_data = {
            'user_id': test_staff.user_id,
            'book_id': book.id,
            'due_date': due_date
        }
        
        response = requests.post(f'{BASE_URL}/api/admin/circulation/issue', 
                               headers=headers, json=issue_data)
        
        if response.status_code == 400 and 'maximum borrowing limit' in response.json().get('error', ''):
            print("✓ Staff borrowing limit correctly enforced")
        else:
            print(f"✗ Staff limit not enforced properly: {response.status_code} - {response.text}")
            return False
        
        # Clean up
        for book in issued_books:
            circulation = Circulation.query.filter_by(
                user_id=test_staff.id, 
                book_id=book.id, 
                status='issued'
            ).first()
            if circulation:
                circulation.status = 'returned'
                circulation.return_date = datetime.now()
                book.available_copies += 1
        
        db.session.commit()
        print("✓ Staff test cleanup completed")
    
    return True

def main():
    """Run all tests"""
    print("Starting Borrowing Limit Enforcement Tests...")
    print("=" * 50)
    
    # Get authentication token
    token = get_auth_token()
    if not token:
        print("✗ Failed to get authentication token")
        return False
    
    print("✓ Authentication successful")
    
    # Run tests
    tests = [
        test_settings_api,
        test_borrowing_limits,
        test_staff_limits
    ]
    
    all_passed = True
    for test in tests:
        try:
            if not test(token):
                all_passed = False
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {str(e)}")
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All tests passed! Borrowing limit enforcement is working correctly.")
    else:
        print("❌ Some tests failed. Please check the implementation.")
    
    return all_passed

if __name__ == "__main__":
    if main():
        sys.exit(0)
    else:
        sys.exit(1)

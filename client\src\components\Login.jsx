import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { BookOpen, User, Lock, AlertCircle } from 'lucide-react'

const Login = () => {
  const [userId, setUserId] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)
  const { login } = useAuth()
  const navigate = useNavigate()

  // Add body class when login component mounts and remove when unmounts
  useEffect(() => {
    document.body.classList.add('login-active')
    return () => {
      document.body.classList.remove('login-active')
    }
  }, [])

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError('')
    setLoading(true)

    if (!userId || !password) {
      setError('Please enter both User ID and password')
      setLoading(false)
      return
    }

    const result = await login(userId, password)

    if (result.success) {
      // Redirect based on role
      const user = result.user
      if (user.role === 'admin') {
        navigate('/admin')
      } else if (user.role === 'librarian') {
        navigate('/librarian')
      } else {
        navigate('/student')
      }
    } else {
      setError(result.error)
    }

    setLoading(false)
  }

  return (
    <div className="login-page">
      <div className="login-container">
      <div className="login-card">
        <div className="login-header">
          <BookOpen size={48} className="login-icon" />
          <h1>Library Management System</h1>
          <p>Sign in to your account</p>
        </div>

        <form onSubmit={handleSubmit} className="login-form">
          {error && (
            <div className="error-message">
              <AlertCircle size={16} />
              {error}
            </div>
          )}

          <div className="form-group">
            <label htmlFor="userId">User ID</label>
            <div className="input-group">
              <User size={20} className="input-icon" />
              <input
                type="text"
                id="userId"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
                placeholder="Enter your User ID (e.g., *********)"
                disabled={loading}
              />
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="password">Password</label>
            <div className="input-group">
              <Lock size={20} className="input-icon" />
              <input
                type="password"
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password"
                disabled={loading}
              />
            </div>
          </div>

          <button
            type="submit"
            className="login-button"
            disabled={loading}
          >
            {loading ? 'Signing in...' : 'Sign In'}
          </button>

          {error && (
            <div className="error-message">
              <AlertCircle size={16} />
              <span>{error}</span>
            </div>
          )}
        </form>

        
        <div className="login-footer">
          <p>
            <button
              type="button"
              className="link-button"
              onClick={() => navigate('/')}
            >
              ← Back to Library Catalog
            </button>
          </p>
          <p>Contact your administrator for account issues</p>
        </div>
      </div>
    </div>
    </div>
  )
}

export default Login

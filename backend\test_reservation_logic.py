#!/usr/bin/env python3
"""
Test script to verify the updated reservation and issuing logic
"""

import requests
import json
from datetime import datetime, timedelta

# Configuration
BASE_URL = 'http://localhost:5000'
ADMIN_CREDENTIALS = {
    'user_id': 'admin',
    'password': 'admin123'
}

def get_admin_token():
    """Get admin authentication token"""
    response = requests.post(f'{BASE_URL}/api/auth/login', json=ADMIN_CREDENTIALS)
    if response.status_code == 200:
        return response.json()['access_token']
    else:
        print(f"Failed to get admin token: {response.text}")
        return None

def test_reservation_logic():
    """Test the reservation and issuing logic"""
    print("🧪 Testing Reservation and Issuing Logic")
    print("=" * 50)
    
    # Get admin token
    token = get_admin_token()
    if not token:
        print("❌ Failed to get admin token")
        return False
    
    headers = {'Authorization': f'Bearer {token}'}
    
    # Test 1: Try to reserve an available book (should fail)
    print("\n📋 Test 1: Student trying to reserve available book")
    
    # First, get a list of available books
    books_response = requests.get(f'{BASE_URL}/api/admin/books', headers=headers)
    if books_response.status_code != 200:
        print(f"❌ Failed to get books: {books_response.text}")
        return False
    
    books = books_response.json().get('books', [])
    available_book = None
    
    for book in books:
        if book.get('available_copies', 0) > 0:
            available_book = book
            break
    
    if not available_book:
        print("⚠️  No available books found for testing")
        return False
    
    print(f"📖 Found available book: {available_book['title']} (Available: {available_book['available_copies']})")
    
    # Test 2: Try to issue a book with reservation conflict
    print("\n📋 Test 2: Testing book issuing with reservation override")
    
    # Get a student user
    users_response = requests.get(f'{BASE_URL}/api/admin/users', headers=headers)
    if users_response.status_code != 200:
        print(f"❌ Failed to get users: {users_response.text}")
        return False
    
    users = users_response.json().get('users', [])
    student_user = None
    
    for user in users:
        if user.get('role') == 'student':
            student_user = user
            break
    
    if not student_user:
        print("⚠️  No student users found for testing")
        return False
    
    print(f"👤 Found student user: {student_user['name']} (ID: {student_user['user_id']})")
    
    # Try to issue the book normally
    due_date = (datetime.now() + timedelta(days=14)).strftime('%Y-%m-%d')
    issue_data = {
        'user_id': student_user['user_id'],
        'book_id': available_book['id'],
        'due_date': due_date
    }
    
    issue_response = requests.post(f'{BASE_URL}/api/admin/circulation/issue', 
                                 headers=headers, json=issue_data)
    
    if issue_response.status_code == 201:
        print("✅ Book issued successfully (normal case)")
        
        # Return the book to test reservation logic
        circulation_id = issue_response.json()['circulation']['id']
        return_data = {'circulation_ids': [circulation_id]}
        
        return_response = requests.post(f'{BASE_URL}/api/admin/circulation/return',
                                      headers=headers, json=return_data)
        
        if return_response.status_code == 200:
            print("✅ Book returned successfully")
        else:
            print(f"⚠️  Failed to return book: {return_response.text}")
    
    elif issue_response.status_code == 409:
        # Reservation conflict - test override
        print("⚠️  Reservation conflict detected")
        conflict_data = issue_response.json()
        
        if conflict_data.get('can_override'):
            print("🔄 Testing reservation override...")
            
            # Try with override
            issue_data['override_reservation'] = True
            override_response = requests.post(f'{BASE_URL}/api/admin/circulation/issue',
                                            headers=headers, json=issue_data)
            
            if override_response.status_code == 201:
                print("✅ Book issued successfully with reservation override")
                
                # Return the book
                circulation_id = override_response.json()['circulation']['id']
                return_data = {'circulation_ids': [circulation_id]}
                
                return_response = requests.post(f'{BASE_URL}/api/admin/circulation/return',
                                              headers=headers, json=return_data)
                
                if return_response.status_code == 200:
                    print("✅ Book returned successfully after override")
                else:
                    print(f"⚠️  Failed to return book after override: {return_response.text}")
            else:
                print(f"❌ Failed to issue book with override: {override_response.text}")
        else:
            print("❌ Override not allowed")
    
    else:
        print(f"❌ Failed to issue book: {issue_response.text}")
    
    print("\n🎉 Reservation logic testing completed!")
    return True

if __name__ == '__main__':
    test_reservation_logic()

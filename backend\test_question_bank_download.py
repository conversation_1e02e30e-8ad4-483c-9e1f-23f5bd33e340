#!/usr/bin/env python3
"""
Test script to verify question bank download functionality
"""

import requests
import json
import os
from datetime import datetime

# Configuration
BASE_URL = 'http://localhost:5000/api'
ADMIN_CREDENTIALS = {
    'user_id': 'admin',
    'password': 'admin123'
}

def get_admin_token():
    """Get admin authentication token"""
    response = requests.post(f'{BASE_URL}/auth/login', json=ADMIN_CREDENTIALS)
    if response.status_code == 200:
        return response.json()['access_token']
    else:
        print(f"Failed to get admin token: {response.text}")
        return None

def test_question_bank_download():
    """Test the question bank download functionality"""
    print("🧪 Testing Question Bank Download Functionality")
    print("=" * 60)
    
    # Get admin token
    token = get_admin_token()
    if not token:
        print("❌ Failed to get admin token")
        return False
    
    headers = {'Authorization': f'Bearer {token}'}
    
    # Test 1: Get list of question banks
    print("\n📋 Test 1: Getting list of question banks")
    
    qb_response = requests.get(f'{BASE_URL}/question-banks/search', headers=headers)
    if qb_response.status_code != 200:
        print(f"❌ Failed to get question banks: {qb_response.text}")
        return False
    
    qb_data = qb_response.json()
    question_banks = qb_data.get('question_banks', [])
    
    if not question_banks:
        print("⚠️  No question banks found for testing")
        return False
    
    print(f"✅ Found {len(question_banks)} question banks")
    
    # Test 2: Debug uploads directory
    print("\n📋 Test 2: Checking uploads directory")
    
    debug_response = requests.get(f'{BASE_URL}/debug/uploads-directory', headers=headers)
    if debug_response.status_code == 200:
        debug_data = debug_response.json()
        print(f"📁 Backend directory: {debug_data.get('backend_directory')}")
        print(f"📁 Uploads directory: {debug_data.get('uploads_directory')}")
        print(f"📁 Uploads exists: {debug_data.get('uploads_exists')}")
        print(f"📁 Files in uploads: {len(debug_data.get('files', []))}")
        print(f"📁 Database records: {len(debug_data.get('database_records', []))}")
        
        # Show file details
        for record in debug_data.get('database_records', []):
            print(f"   - ID {record['id']}: {record['file_name']} -> {record['file_path']} (exists: {record['file_exists']})")
    else:
        print(f"⚠️  Could not get debug info: {debug_response.text}")
    
    # Test 3: Test question bank info endpoint
    print("\n📋 Test 3: Testing question bank info endpoint")
    
    test_qb = question_banks[0]
    qb_id = test_qb['id']
    
    info_response = requests.get(f'{BASE_URL}/question-banks/{qb_id}/info', headers=headers)
    if info_response.status_code == 200:
        info_data = info_response.json()
        print(f"📄 Question Bank ID: {info_data.get('id')}")
        print(f"📄 File Name: {info_data.get('file_name')}")
        print(f"📄 Stored Path: {info_data.get('stored_file_path')}")
        print(f"📄 Resolved Path: {info_data.get('resolved_file_path')}")
        print(f"📄 File Exists: {info_data.get('file_exists')}")
        print(f"📄 File Size: {info_data.get('file_size')}")
        print(f"📄 Download Count: {info_data.get('download_count')}")
    else:
        print(f"❌ Failed to get question bank info: {info_response.text}")
    
    # Test 4: Test actual download
    print("\n📋 Test 4: Testing actual download")
    
    download_response = requests.get(f'{BASE_URL}/question-banks/{qb_id}/download', headers=headers)
    
    if download_response.status_code == 200:
        print(f"✅ Download successful!")
        print(f"📄 Content-Type: {download_response.headers.get('Content-Type')}")
        print(f"📄 Content-Length: {download_response.headers.get('Content-Length')}")
        print(f"📄 Content-Disposition: {download_response.headers.get('Content-Disposition')}")
        
        # Save file for verification
        filename = f"test_download_{qb_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        with open(filename, 'wb') as f:
            f.write(download_response.content)
        
        file_size = os.path.getsize(filename)
        print(f"📄 Downloaded file size: {file_size} bytes")
        
        # Clean up test file
        os.remove(filename)
        print(f"📄 Test file cleaned up")
        
    else:
        print(f"❌ Download failed: {download_response.status_code}")
        print(f"❌ Error: {download_response.text}")
    
    print("\n🎉 Question bank download testing completed!")
    return True

if __name__ == '__main__':
    test_question_bank_download()

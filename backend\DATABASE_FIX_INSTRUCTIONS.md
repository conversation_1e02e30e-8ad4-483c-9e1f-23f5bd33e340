# Database Schema Fix Instructions

## Problem
The `circulations` table is missing the `fine_amount` column, causing errors when trying to issue/return books or manage users.

## Solutions (Choose One)

### Option 1: Automatic Migration (Recommended)
1. **Stop the current server** (Ctrl+C)
2. **Restart the server**:
   ```bash
   cd backend
   python app.py
   ```
3. **Look for migration messages** in the console output
4. **Test the application** - try searching for a user in Issue Book page

### Option 2: Manual Migration (If Option 1 fails)
1. **Stop the server** (Ctrl+C)
2. **Run the migration script**:
   ```bash
   cd backend
   python migrate_database.py
   ```
3. **Restart the server**:
   ```bash
   python app.py
   ```

### Option 3: Complete Database Reset (If all else fails)
⚠️ **Warning: This will delete all existing data!**

1. **Stop the server** (Ctrl+C)
2. **Run the reset script**:
   ```bash
   cd backend
   python reset_database.py
   ```
3. **Follow the prompts** (type 'yes' to confirm)
4. **Restart the server**:
   ```bash
   python app.py
   ```

## What Each Option Does

### Option 1 (Automatic Migration)
- Checks if the `fine_amount` column exists
- Adds it if missing
- Preserves all existing data
- Creates default admin user if needed

### Option 2 (Manual Migration)
- Directly adds the missing column using SQLite commands
- Preserves all existing data
- Verifies the schema after migration

### Option 3 (Database Reset)
- Deletes the entire database
- Creates a new database with correct schema
- Adds sample data for testing
- Creates default users:
  - Admin: <EMAIL> / admin123
  - Librarian: <EMAIL> / lib123
  - Student: <EMAIL> / student123

## Verification Steps

After running any of the above options:

1. **Check console output** for success messages
2. **Try the Issue Book page**:
   - Go to Admin Dashboard → Circulation → Issue Book
   - Enter a user ID (e.g., "STU001" if you used Option 3)
   - Should show user information without errors
3. **Try the Return Book page**:
   - Go to Admin Dashboard → Circulation → Return Book
   - Enter the same user ID
   - Should show borrowed books without errors

## Troubleshooting

### If you still get errors after trying all options:

1. **Check file permissions** - make sure the backend folder is writable
2. **Check Python version** - ensure you're using Python 3.7+
3. **Reinstall dependencies**:
   ```bash
   cd backend
   pip install -r requirements.txt
   ```
4. **Manual database deletion**:
   ```bash
   cd backend
   rm library.db
   python app.py
   ```

### Common Error Messages and Solutions:

- **"no such column: circulations.fine_amount"** → Run Option 1 or 2
- **"database is locked"** → Stop all Python processes and try again
- **"permission denied"** → Check folder permissions
- **"module not found"** → Reinstall dependencies

## Need Help?

If none of these solutions work, please provide:
1. The exact error message
2. Your operating system
3. Python version (`python --version`)
4. Console output from the migration attempts

The database schema should now be fixed and all circulation features should work properly!

import React, { useState, useEffect } from 'react'
import { User, Book, IndianRupee, Calendar, ArrowLeft, Clock, CheckCircle, AlertCircle } from 'lucide-react'
import axios from 'axios'

const StudentProfile = ({ studentId, onBack }) => {
  const [studentData, setStudentData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('current')

  useEffect(() => {
    if (studentId) {
      fetchStudentProfile()
    }
  }, [studentId])

  const fetchStudentProfile = async () => {
    try {
      setLoading(true)
      
      // Get user circulation info (includes current books and history)
      const circulationResponse = await axios.get(`/admin/circulation/user/${studentId}`)
      
      // Get user fines
      const finesResponse = await axios.get(`/admin/fines/user/${studentId}`)
      
      setStudentData({
        user: circulationResponse.data.user,
        currentBooks: circulationResponse.data.current_books,
        borrowingHistory: circulationResponse.data.borrowing_history,
        fines: finesResponse.data.fines,
        totalFine: finesResponse.data.total_pending
      })
    } catch (error) {
      console.error('Failed to fetch student profile:', error)
      alert('Failed to load student profile')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="student-profile">
        <div className="loading">Loading student profile...</div>
      </div>
    )
  }

  if (!studentData) {
    return (
      <div className="student-profile">
        <div className="error">Failed to load student profile</div>
      </div>
    )
  }

  const { user, currentBooks, borrowingHistory, fines, totalFine } = studentData

  return (
    <div className="student-profile">
      <div className="profile-header">
        <button className="back-button" onClick={onBack}>
          <ArrowLeft size={16} />
          Back to Students
        </button>
        <h1>Student Profile</h1>
      </div>

      {/* Student Information */}
      <div className="profile-info-card">
        <div className="student-avatar">
          <User size={48} />
        </div>
        <div className="student-details">
          <h2>{user.name}</h2>
          <p><strong>Student ID:</strong> {user.user_id}</p>
          <p><strong>Email:</strong> {user.email}</p>
          {user.college && <p><strong>College:</strong> {user.college}</p>}
          {user.department && <p><strong>Department:</strong> {user.department}</p>}
        </div>
        <div className="profile-stats">
          <div className="stat-card">
            <div className="stat-icon">
              <Book size={24} />
            </div>
            <div className="stat-info">
              <span className="stat-number">{currentBooks.length}</span>
              <span className="stat-label">Current Books</span>
            </div>
          </div>
          <div className="stat-card">
            <div className="stat-icon">
              <DollarSign size={24} />
            </div>
            <div className="stat-info">
              <span className="stat-number">${totalFine.toFixed(2)}</span>
              <span className="stat-label">Outstanding Fines</span>
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="profile-tabs">
        <button 
          className={`tab ${activeTab === 'current' ? 'active' : ''}`}
          onClick={() => setActiveTab('current')}
        >
          Current Books ({currentBooks.length})
        </button>
        <button 
          className={`tab ${activeTab === 'history' ? 'active' : ''}`}
          onClick={() => setActiveTab('history')}
        >
          Borrowing History ({borrowingHistory.length})
        </button>
        <button 
          className={`tab ${activeTab === 'fines' ? 'active' : ''}`}
          onClick={() => setActiveTab('fines')}
        >
          Fine History ({fines.length})
        </button>
      </div>

      {/* Tab Content */}
      <div className="tab-content">
        {activeTab === 'current' && (
          <div className="current-books">
            <h3>Currently Borrowed Books</h3>
            {currentBooks.length === 0 ? (
              <div className="no-data">
                <Book size={48} />
                <p>No books currently borrowed</p>
              </div>
            ) : (
              <div className="books-grid">
                {currentBooks.map((item) => (
                  <div key={item.circulation_id} className={`book-card ${item.is_overdue ? 'overdue' : ''}`}>
                    <div className="book-header">
                      <h4>{item.title}</h4>
                      <div className={`status-badge ${item.is_overdue ? 'overdue' : 'on-time'}`}>
                        {item.is_overdue ? (
                          <>
                            <Clock size={12} />
                            Overdue
                          </>
                        ) : (
                          <>
                            <CheckCircle size={12} />
                            On Time
                          </>
                        )}
                      </div>
                    </div>
                    <p><strong>Author:</strong> {item.author}</p>
                    <p><strong>Access No:</strong> {item.access_no}</p>
                    <div className="book-dates">
                      <p><strong>Issued:</strong> {new Date(item.issue_date).toLocaleDateString()}</p>
                      <p><strong>Due:</strong> {new Date(item.due_date).toLocaleDateString()}</p>
                      {item.is_overdue && (
                        <div className="overdue-info">
                          <AlertCircle size={14} />
                          <span>{item.days_overdue} days overdue</span>
                          {item.fine_amount > 0 && (
                            <span className="fine-amount">₹{item.fine_amount.toFixed(2)} fine</span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'history' && (
          <div className="borrowing-history">
            <h3>Borrowing History</h3>
            {borrowingHistory.length === 0 ? (
              <div className="no-data">
                <Calendar size={48} />
                <p>No borrowing history</p>
              </div>
            ) : (
              <div className="history-table">
                <table>
                  <thead>
                    <tr>
                      <th>Book Title</th>
                      <th>Author</th>
                      <th>Issue Date</th>
                      <th>Due Date</th>
                      <th>Return Date</th>
                      <th>Status</th>
                      <th>Fine</th>
                    </tr>
                  </thead>
                  <tbody>
                    {borrowingHistory.map((item, index) => (
                      <tr key={index}>
                        <td>{item.book_title}</td>
                        <td>{item.author}</td>
                        <td>{new Date(item.issue_date).toLocaleDateString()}</td>
                        <td>{new Date(item.due_date).toLocaleDateString()}</td>
                        <td>{item.return_date ? new Date(item.return_date).toLocaleDateString() : '-'}</td>
                        <td>
                          <span className={`status ${item.status}`}>
                            {item.status}
                          </span>
                        </td>
                        <td>
                          {item.fine_amount > 0 ? `₹${item.fine_amount.toFixed(2)}` : '-'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {activeTab === 'fines' && (
          <div className="fine-history">
            <h3>Fine History</h3>
            {fines.length === 0 ? (
              <div className="no-data">
                <IndianRupee size={48} />
                <p>No fine history</p>
              </div>
            ) : (
              <div className="fines-table">
                <table>
                  <thead>
                    <tr>
                      <th>Amount</th>
                      <th>Reason</th>
                      <th>Status</th>
                      <th>Created Date</th>
                      <th>Paid Date</th>
                    </tr>
                  </thead>
                  <tbody>
                    {fines.map((fine) => (
                      <tr key={fine.id}>
                        <td className="amount">₹{fine.amount.toFixed(2)}</td>
                        <td>{fine.reason}</td>
                        <td>
                          <span className={`status ${fine.status}`}>
                            {fine.status === 'paid' ? (
                              <>
                                <CheckCircle size={12} />
                                Paid
                              </>
                            ) : (
                              <>
                                <AlertCircle size={12} />
                                Pending
                              </>
                            )}
                          </span>
                        </td>
                        <td>{new Date(fine.created_date).toLocaleDateString()}</td>
                        <td>{fine.paid_date ? new Date(fine.paid_date).toLocaleDateString() : '-'}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default StudentProfile

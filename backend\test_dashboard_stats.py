#!/usr/bin/env python3
"""
Test script to verify dashboard statistics are showing real data
"""

import requests
import json
from datetime import datetime

# Configuration
BASE_URL = 'http://localhost:5000/api'
ADMIN_CREDENTIALS = {
    'user_id': 'admin',
    'password': 'admin123'
}

def get_admin_token():
    """Get admin authentication token"""
    response = requests.post(f'{BASE_URL}/auth/login', json=ADMIN_CREDENTIALS)
    if response.status_code == 200:
        return response.json()['access_token']
    else:
        print(f"Failed to get admin token: {response.text}")
        return None

def test_dashboard_stats():
    """Test the dashboard statistics endpoint"""
    print("🧪 Testing Dashboard Statistics")
    print("=" * 40)
    
    # Get admin token
    token = get_admin_token()
    if not token:
        print("❌ Failed to get admin token")
        return False
    
    headers = {'Authorization': f'Bearer {token}'}
    
    # Test 1: Get dashboard stats
    print("\n📊 Test 1: Getting dashboard statistics")
    
    stats_response = requests.get(f'{BASE_URL}/admin/dashboard-stats', headers=headers)
    print(f"Status Code: {stats_response.status_code}")
    
    if stats_response.status_code == 200:
        stats_data = stats_response.json()
        print(f"✅ Successfully fetched dashboard stats")
        print("\n📈 Dashboard Statistics:")
        print(f"   📚 Total Books: {stats_data.get('totalBooks', 'N/A')}")
        print(f"   💻 Total E-books: {stats_data.get('totalEbooks', 'N/A')}")
        print(f"   🎓 Total Students: {stats_data.get('totalStudents', 'N/A')}")
        print(f"   👥 Total Librarians: {stats_data.get('totalLibrarians', 'N/A')}")
        print(f"   🏢 Total Colleges: {stats_data.get('totalColleges', 'N/A')}")
        print(f"   🔄 Active Circulations: {stats_data.get('activeCirculations', 'N/A')}")
        print(f"   📖 Available Books: {stats_data.get('availableBooks', 'N/A')}")
        print(f"   💰 Total Fines: {stats_data.get('totalFines', 'N/A')}")
        print(f"   ⚠️  Overdue Books: {stats_data.get('overdueBooks', 'N/A')}")
        
        # Test 2: Verify e-book count by getting actual e-books
        print("\n📊 Test 2: Verifying e-book count")
        
        ebooks_response = requests.get(f'{BASE_URL}/admin/ebooks', headers=headers)
        if ebooks_response.status_code == 200:
            ebooks_data = ebooks_response.json()
            actual_ebook_count = len(ebooks_data.get('ebooks', []))
            dashboard_ebook_count = stats_data.get('totalEbooks', 0)
            
            print(f"📚 E-books from dashboard: {dashboard_ebook_count}")
            print(f"📚 E-books from direct query: {actual_ebook_count}")
            
            if dashboard_ebook_count == actual_ebook_count:
                print("✅ E-book counts match! Dashboard is showing real data.")
            else:
                print("❌ E-book counts don't match! There's still a data inconsistency.")
                
                # Show detailed e-book info
                print("\n📋 E-books in system:")
                for i, ebook in enumerate(ebooks_data.get('ebooks', []), 1):
                    print(f"   {i}. {ebook.get('access_no')}: {ebook.get('web_title')} ({ebook.get('type')})")
        else:
            print(f"❌ Failed to fetch e-books for verification: {ebooks_response.text}")
        
        # Test 3: Verify other counts
        print("\n📊 Test 3: Verifying other statistics")
        
        # Check books count
        books_response = requests.get(f'{BASE_URL}/admin/books', headers=headers)
        if books_response.status_code == 200:
            books_data = books_response.json()
            actual_books_count = len(books_data.get('books', []))
            dashboard_books_count = stats_data.get('totalBooks', 0)
            
            print(f"📚 Books from dashboard: {dashboard_books_count}")
            print(f"📚 Books from direct query: {actual_books_count}")
            
            if dashboard_books_count == actual_books_count:
                print("✅ Book counts match!")
            else:
                print("⚠️  Book counts don't match (this might be due to pagination)")
        
        return True
    else:
        print(f"❌ Failed to fetch dashboard stats: {stats_response.text}")
        return False

def test_database_counts():
    """Test database counts directly"""
    print("\n🗄️  Testing Database Counts Directly")
    print("=" * 40)
    
    # This would require direct database access
    # For now, we'll just show what we can test via API
    
    token = get_admin_token()
    if not token:
        return False
    
    headers = {'Authorization': f'Bearer {token}'}
    
    # Get all e-books with pagination to count total
    page = 1
    total_ebooks = 0
    
    while True:
        response = requests.get(f'{BASE_URL}/admin/ebooks?page={page}&per_page=100', headers=headers)
        if response.status_code == 200:
            data = response.json()
            ebooks = data.get('ebooks', [])
            total_ebooks += len(ebooks)
            
            pagination = data.get('pagination', {})
            if not pagination.get('has_next', False):
                break
            page += 1
        else:
            break
    
    print(f"📚 Total e-books counted via API: {total_ebooks}")
    
    return True

if __name__ == '__main__':
    success = test_dashboard_stats()
    if success:
        test_database_counts()
        print("\n🎉 Dashboard statistics testing completed!")
    else:
        print("\n❌ Dashboard statistics testing failed!")

#!/usr/bin/env python3
"""
Test script to verify account expiration functionality is working correctly.
This script tests that expired users are properly filtered out and cannot access the system.
"""

import sys
import os
import requests
import json
from datetime import datetime, date, timedelta

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, User, Book, Circulation
from werkzeug.security import generate_password_hash

# Test configuration
BASE_URL = 'http://localhost:5000'
TEST_ADMIN_CREDENTIALS = {
    'user_id': 'admin',
    'password': 'admin123'
}

def get_auth_token():
    """Get authentication token for admin user"""
    response = requests.post(f'{BASE_URL}/api/auth/login', json=TEST_ADMIN_CREDENTIALS)
    if response.status_code == 200:
        return response.json()['access_token']
    else:
        print(f"Failed to authenticate: {response.text}")
        return None

def create_test_users():
    """Create test users with different expiration dates"""
    print("\n=== Creating Test Users ===")
    
    with app.app_context():
        try:
            # Clean up existing test users
            User.query.filter(User.user_id.like('test_%')).delete()
            db.session.commit()
            
            # Create test users
            today = date.today()
            
            # User 1: Expired 5 days ago
            expired_user = User(
                user_id='test_expired',
                username='testexpired',
                name='Test Expired User',
                email='<EMAIL>',
                password_hash=generate_password_hash('password123'),
                role='student',
                designation='Student',
                dob=date(2000, 1, 1),
                validity_date=today - timedelta(days=5),
                is_active=True
            )
            
            # User 2: Expires today
            expires_today_user = User(
                user_id='test_expires_today',
                username='testexpirestoday',
                name='Test Expires Today User',
                email='<EMAIL>',
                password_hash=generate_password_hash('password123'),
                role='student',
                designation='Student',
                dob=date(2000, 1, 1),
                validity_date=today,
                is_active=True
            )
            
            # User 3: Expires in 3 days (expires soon)
            expires_soon_user = User(
                user_id='test_expires_soon',
                username='testexpiressoon',
                name='Test Expires Soon User',
                email='<EMAIL>',
                password_hash=generate_password_hash('password123'),
                role='student',
                designation='Student',
                dob=date(2000, 1, 1),
                validity_date=today + timedelta(days=3),
                is_active=True
            )
            
            # User 4: Valid for 30 days
            valid_user = User(
                user_id='test_valid',
                username='testvalid',
                name='Test Valid User',
                email='<EMAIL>',
                password_hash=generate_password_hash('password123'),
                role='student',
                designation='Student',
                dob=date(2000, 1, 1),
                validity_date=today + timedelta(days=30),
                is_active=True
            )
            
            # User 5: Inactive but not expired
            inactive_user = User(
                user_id='test_inactive',
                username='testinactive',
                name='Test Inactive User',
                email='<EMAIL>',
                password_hash=generate_password_hash('password123'),
                role='student',
                designation='Student',
                dob=date(2000, 1, 1),
                validity_date=today + timedelta(days=30),
                is_active=False
            )
            
            db.session.add_all([expired_user, expires_today_user, expires_soon_user, valid_user, inactive_user])
            db.session.commit()
            
            print("✓ Created test users:")
            print(f"  - test_expired (expired {5} days ago)")
            print(f"  - test_expires_today (expires today)")
            print(f"  - test_expires_soon (expires in 3 days)")
            print(f"  - test_valid (valid for 30 days)")
            print(f"  - test_inactive (inactive but not expired)")
            
            return True
            
        except Exception as e:
            print(f"✗ Error creating test users: {str(e)}")
            return False

def test_login_restrictions():
    """Test that expired users cannot log in"""
    print("\n=== Testing Login Restrictions ===")
    
    test_cases = [
        ('test_expired', 'password123', 401, 'Should reject expired user'),
        ('test_expires_today', 'password123', 401, 'Should reject user expiring today'),
        ('test_expires_soon', 'password123', 200, 'Should allow user expiring soon'),
        ('test_valid', 'password123', 200, 'Should allow valid user'),
        ('test_inactive', 'password123', 401, 'Should reject inactive user')
    ]
    
    all_passed = True
    
    for user_id, password, expected_status, description in test_cases:
        response = requests.post(f'{BASE_URL}/api/auth/login', json={
            'user_id': user_id,
            'password': password
        })
        
        if response.status_code == expected_status:
            print(f"✓ {description}: {user_id} -> {response.status_code}")
        else:
            print(f"✗ {description}: {user_id} -> Expected {expected_status}, got {response.status_code}")
            print(f"  Response: {response.text}")
            all_passed = False
    
    return all_passed

def test_user_listing():
    """Test that expired users are filtered from user listings"""
    print("\n=== Testing User Listing Filters ===")
    
    token = get_auth_token()
    if not token:
        return False
    
    headers = {'Authorization': f'Bearer {token}'}
    
    # Test default behavior (should exclude expired users)
    response = requests.get(f'{BASE_URL}/api/admin/users?role=student', headers=headers)
    if response.status_code == 200:
        users = response.json()['users']
        test_user_ids = [u['user_id'] for u in users if u['user_id'].startswith('test_')]
        
        # Should only include expires_soon and valid users
        expected_users = ['test_expires_soon', 'test_valid']
        if set(test_user_ids) == set(expected_users):
            print(f"✓ Default listing correctly excludes expired users: {test_user_ids}")
        else:
            print(f"✗ Default listing incorrect. Expected {expected_users}, got {test_user_ids}")
            return False
    else:
        print(f"✗ Failed to get user listing: {response.text}")
        return False
    
    # Test with include_expired=true
    response = requests.get(f'{BASE_URL}/api/admin/users?role=student&include_expired=true', headers=headers)
    if response.status_code == 200:
        users = response.json()['users']
        test_user_ids = [u['user_id'] for u in users if u['user_id'].startswith('test_')]
        
        # Should include all test users except inactive
        expected_users = ['test_expired', 'test_expires_today', 'test_expires_soon', 'test_valid']
        if set(test_user_ids) == set(expected_users):
            print(f"✓ Include expired listing works correctly: {test_user_ids}")
        else:
            print(f"✗ Include expired listing incorrect. Expected {expected_users}, got {test_user_ids}")
            return False
    else:
        print(f"✗ Failed to get user listing with expired: {response.text}")
        return False
    
    return True

def test_book_issuing():
    """Test that books cannot be issued to expired users"""
    print("\n=== Testing Book Issuing Restrictions ===")
    
    token = get_auth_token()
    if not token:
        return False
    
    headers = {'Authorization': f'Bearer {token}'}
    
    # Find an available book
    with app.app_context():
        available_book = Book.query.filter(Book.available_copies > 0).first()
        if not available_book:
            print("✗ No available books for testing")
            return False
    
    due_date = (datetime.now() + timedelta(days=14)).strftime('%Y-%m-%d')
    
    test_cases = [
        ('test_expired', 400, 'Should reject issuing to expired user'),
        ('test_expires_today', 400, 'Should reject issuing to user expiring today'),
        ('test_expires_soon', 201, 'Should allow issuing to user expiring soon'),
        ('test_valid', 201, 'Should allow issuing to valid user'),
        ('test_inactive', 400, 'Should reject issuing to inactive user')
    ]
    
    all_passed = True
    
    for user_id, expected_status, description in test_cases:
        issue_data = {
            'user_id': user_id,
            'book_id': available_book.id,
            'due_date': due_date
        }
        
        response = requests.post(f'{BASE_URL}/api/admin/circulation/issue', 
                               headers=headers, json=issue_data)
        
        if response.status_code == expected_status:
            print(f"✓ {description}: {user_id} -> {response.status_code}")
            
            # If successful, clean up by returning the book
            if response.status_code == 201:
                with app.app_context():
                    user = User.query.filter_by(user_id=user_id).first()
                    circulation = Circulation.query.filter_by(
                        user_id=user.id, 
                        book_id=available_book.id, 
                        status='issued'
                    ).first()
                    if circulation:
                        circulation.status = 'returned'
                        circulation.return_date = datetime.now()
                        available_book.available_copies += 1
                        db.session.commit()
        else:
            print(f"✗ {description}: {user_id} -> Expected {expected_status}, got {response.status_code}")
            print(f"  Response: {response.text}")
            all_passed = False
    
    return all_passed

def test_user_search():
    """Test that expired users don't appear in search results"""
    print("\n=== Testing User Search Functionality ===")
    
    token = get_auth_token()
    if not token:
        return False
    
    headers = {'Authorization': f'Bearer {token}'}
    
    # Test searching for expired user
    response = requests.get(f'{BASE_URL}/api/admin/circulation/user/test_expired', headers=headers)
    if response.status_code == 400:
        error_data = response.json()
        if 'expired' in error_data.get('error', '').lower():
            print("✓ Search correctly rejects expired user")
        else:
            print(f"✗ Unexpected error for expired user: {error_data}")
            return False
    else:
        print(f"✗ Expected 400 for expired user search, got {response.status_code}")
        return False
    
    # Test searching for valid user
    response = requests.get(f'{BASE_URL}/api/admin/circulation/user/test_valid', headers=headers)
    if response.status_code == 200:
        print("✓ Search correctly allows valid user")
    else:
        print(f"✗ Expected 200 for valid user search, got {response.status_code}")
        return False
    
    return True

def cleanup_test_users():
    """Clean up test users"""
    print("\n=== Cleaning Up Test Users ===")
    
    with app.app_context():
        try:
            # Clean up circulations first
            test_users = User.query.filter(User.user_id.like('test_%')).all()
            for user in test_users:
                Circulation.query.filter_by(user_id=user.id).delete()
            
            # Clean up users
            User.query.filter(User.user_id.like('test_%')).delete()
            db.session.commit()
            
            print("✓ Test users cleaned up")
            return True
            
        except Exception as e:
            print(f"✗ Error cleaning up test users: {str(e)}")
            return False

def main():
    """Run all account expiration tests"""
    print("Starting Account Expiration Functionality Tests...")
    print("=" * 60)
    
    # Create test users
    if not create_test_users():
        print("❌ Failed to create test users")
        return False
    
    # Run tests
    tests = [
        test_login_restrictions,
        test_user_listing,
        test_book_issuing,
        test_user_search
    ]
    
    all_passed = True
    for test in tests:
        try:
            if not test():
                all_passed = False
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {str(e)}")
            all_passed = False
    
    # Clean up
    cleanup_test_users()
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 All account expiration tests passed! The functionality is working correctly.")
        print("\nKey features verified:")
        print("✓ Expired users cannot log in")
        print("✓ Expired users are filtered from admin dashboard")
        print("✓ Books cannot be issued to expired users")
        print("✓ User search excludes expired accounts")
        print("✓ Account status information is properly displayed")
    else:
        print("❌ Some tests failed. Please check the implementation.")
    
    return all_passed

if __name__ == "__main__":
    if main():
        sys.exit(0)
    else:
        sys.exit(1)

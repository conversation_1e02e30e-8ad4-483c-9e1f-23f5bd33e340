#!/usr/bin/env python3
"""
Test script to verify e-book endpoints functionality
"""

import requests
import json
from datetime import datetime

# Configuration
BASE_URL = 'http://localhost:5000/api'
ADMIN_CREDENTIALS = {
    'user_id': 'admin',
    'password': 'admin123'
}

def get_admin_token():
    """Get admin authentication token"""
    response = requests.post(f'{BASE_URL}/auth/login', json=ADMIN_CREDENTIALS)
    if response.status_code == 200:
        return response.json()['access_token']
    else:
        print(f"Failed to get admin token: {response.text}")
        return None

def test_ebook_endpoints():
    """Test the e-book endpoints functionality"""
    print("🧪 Testing E-book Endpoints Functionality")
    print("=" * 50)
    
    # Get admin token
    token = get_admin_token()
    if not token:
        print("❌ Failed to get admin token")
        return False
    
    headers = {'Authorization': f'Bearer {token}'}
    
    # Test 1: Get list of e-books
    print("\n📋 Test 1: Getting list of e-books")
    
    ebooks_response = requests.get(f'{BASE_URL}/admin/ebooks', headers=headers)
    print(f"Status Code: {ebooks_response.status_code}")
    
    if ebooks_response.status_code == 200:
        ebooks_data = ebooks_response.json()
        print(f"✅ Successfully fetched e-books")
        print(f"📚 Total e-books: {len(ebooks_data.get('ebooks', []))}")
        print(f"📄 Pagination: {ebooks_data.get('pagination', {})}")
        
        # Show first few e-books
        for ebook in ebooks_data.get('ebooks', [])[:3]:
            print(f"   - {ebook.get('access_no')}: {ebook.get('web_title')} ({ebook.get('type')})")
    else:
        print(f"❌ Failed to fetch e-books: {ebooks_response.text}")
        return False
    
    # Test 2: Get next access number
    print("\n📋 Test 2: Getting next access number")
    
    next_access_response = requests.get(f'{BASE_URL}/admin/ebooks/next-access-number', headers=headers)
    if next_access_response.status_code == 200:
        next_access_data = next_access_response.json()
        print(f"✅ Next access number: {next_access_data.get('next_access_number')}")
    else:
        print(f"❌ Failed to get next access number: {next_access_response.text}")
    
    # Test 3: Create a new e-book
    print("\n📋 Test 3: Creating a new e-book")
    
    test_ebook_data = {
        'access_no': f'TEST_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
        'website': 'https://example.com/test-ebook',
        'web_title': 'Test E-book for API Testing',
        'subject': 'Computer Science',
        'type': 'E-book',
        'web_detail': 'This is a test e-book created by the API test script'
    }
    
    create_response = requests.post(f'{BASE_URL}/admin/ebooks', headers=headers, json=test_ebook_data)
    print(f"Status Code: {create_response.status_code}")
    
    if create_response.status_code == 201:
        create_data = create_response.json()
        print(f"✅ Successfully created e-book")
        print(f"📚 Created e-book: {create_data.get('ebook', {})}")
        test_ebook_id = create_data.get('ebook', {}).get('id')
        
        # Test 4: Update the e-book
        print("\n📋 Test 4: Updating the e-book")
        
        update_data = {
            'access_no': test_ebook_data['access_no'],
            'website': test_ebook_data['website'],
            'web_title': 'Updated Test E-book for API Testing',
            'subject': 'Updated Computer Science',
            'type': 'E-journal',
            'web_detail': 'This is an updated test e-book'
        }
        
        update_response = requests.put(f'{BASE_URL}/admin/ebooks/{test_ebook_id}', headers=headers, json=update_data)
        if update_response.status_code == 200:
            print(f"✅ Successfully updated e-book")
        else:
            print(f"❌ Failed to update e-book: {update_response.text}")
        
        # Test 5: Delete the test e-book
        print("\n📋 Test 5: Deleting the test e-book")
        
        delete_response = requests.delete(f'{BASE_URL}/admin/ebooks/{test_ebook_id}', headers=headers)
        if delete_response.status_code == 200:
            print(f"✅ Successfully deleted test e-book")
        else:
            print(f"❌ Failed to delete test e-book: {delete_response.text}")
            
    else:
        print(f"❌ Failed to create e-book: {create_response.text}")
    
    # Test 6: Test search functionality
    print("\n📋 Test 6: Testing search functionality")
    
    search_response = requests.get(f'{BASE_URL}/admin/ebooks?search=computer', headers=headers)
    if search_response.status_code == 200:
        search_data = search_response.json()
        print(f"✅ Search successful, found {len(search_data.get('ebooks', []))} results")
    else:
        print(f"❌ Search failed: {search_response.text}")
    
    print("\n🎉 E-book endpoints testing completed!")
    return True

if __name__ == '__main__':
    test_ebook_endpoints()
